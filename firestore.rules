rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return request.auth.uid == userId;
    }

    match /users/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    match /user_consents/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // The Corrected Journal Entries Rule
    match /journal_entries/{entryId} {
      allow create, update, delete: if isAuthenticated() && isOwner(request.resource.data.userId);

      allow read: if isAuthenticated() &&
        (
          isOwner(resource.data.userId) ||
          (
            resource.data.isShared == true &&
            get(/databases/$(database)/documents/users/$(request.auth.uid)).data.partnerId == resource.data.userId
          )
        );
    }

    match /partnerships/{partnershipId} {
      allow read: if isAuthenticated() && 
        (request.auth.uid == resource.data.primaryUserUid || 
         request.auth.uid == resource.data.connectedUserUid);
      allow create, update, delete: if false; // Only Cloud Functions can manage partnerships
    }

    match /audit_logs/{logId} {
      allow create: if isAuthenticated();
      allow read: if isAuthenticated() && isOwner(resource.data.userId);
      allow update, delete: if false;
    }

    match /deletion_requests/{requestId} {
      allow create, read, update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if false; // Only Cloud Functions can delete
    }

    match /data_retention/{retentionId} {
      allow read: if isAuthenticated() && isOwner(resource.data.userId);
      allow create, update, delete: if false; // Only Cloud Functions can manage
    }

    match /invites/{inviteId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && isOwner(request.resource.data.fromUserId);
      allow update: if isAuthenticated();
      allow delete: if false; // Only Cloud Functions can delete
    }
  }
}
