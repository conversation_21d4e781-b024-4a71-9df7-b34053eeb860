@import "tailwindcss";

@theme {
  /* You can define custom theme values here if needed */
}

/* Apply the gradient background to the entire page body */
body {
  @apply bg-gradient-to-br from-red-300 to-green-300;
}

/* Custom animations for mood logging UX */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gentleGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(255, 138, 128, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 138, 128, 0.6);
  }
}

@keyframes successPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-gentleGlow {
  animation: gentleGlow 2s ease-in-out infinite;
}

.animate-successPulse {
  animation: successPulse 0.6s ease-out;
}

/* Custom border width for mood buttons */
.border-3 {
  border-width: 3px;
}

/* Smooth transitions for form elements */
.transition-gentle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
