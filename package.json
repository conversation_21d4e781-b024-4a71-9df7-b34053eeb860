{"name": "metiscorehealth-ecosystem", "version": "1.0.0", "private": true, "description": "Monorepo for MenoWellness and Partner Support apps with secure backend and shared modules.", "author": "<PERSON>", "packageManager": "pnpm@10.12.4", "scripts": {"dev:frontend": "concurrently \"pnpm --filter=meno-wellness dev\" \"pnpm --filter=partner-support dev\" --names \"meno,partner\" --prefix-colors \"cyan,magenta\"", "dev:backend": "pnpm --filter=backend dev", "build": "pnpm --recursive build", "lint": "pnpm --recursive lint", "typecheck": "pnpm --recursive run typecheck", "clean": "pnpm --recursive exec rm -rf dist .next", "install:all": "pnpm install", "deploy": "firebase deploy"}, "dependencies": {"firebase": "^11.10.0", "next": "15.3.4", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"concurrently": "^9.2.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5.4.5"}}